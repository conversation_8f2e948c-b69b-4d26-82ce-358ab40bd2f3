import { AIModel, ModelParameters, PromptTemplate } from '@/types';

export const AI_MODELS: AIModel[] = [
  {
    id: 'gpt-4',
    name: 'GPT-4',
    provider: 'OpenAI',
    description: 'Most capable model for complex reasoning and creative tasks',
    maxTokens: 8192,
    costPer1kTokens: 0.03,
    icon: '🧠'
  },
  {
    id: 'gpt-3.5-turbo',
    name: 'GPT-3.5 Turbo',
    provider: 'OpenAI',
    description: 'Fast and efficient for most conversational tasks',
    maxTokens: 4096,
    costPer1kTokens: 0.002,
    icon: '⚡'
  },
  {
    id: 'claude-3',
    name: 'Claude 3',
    provider: 'Anthropic',
    description: 'Excellent for analysis, reasoning, and ethical discussions',
    maxTokens: 8192,
    costPer1kTokens: 0.015,
    icon: '🎭'
  },
  {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    provider: 'Google',
    description: 'Powerful multimodal capabilities with long context',
    maxTokens: 32768,
    costPer1kTokens: 0.001,
    icon: '💎'
  },
  {
    id: 'custom',
    name: 'Custom Model',
    provider: 'Local',
    description: 'Your own fine-tuned model for specialized tasks',
    maxTokens: 2048,
    costPer1kTokens: 0.0,
    icon: '🔧'
  }
];

export const DEFAULT_PARAMETERS: ModelParameters = {
  temperature: 0.7,
  maxTokens: 1000,
  topP: 0.9,
  frequencyPenalty: 0,
  presencePenalty: 0
};

export const PARAMETER_PRESETS = {
  creative: {
    temperature: 0.8,
    maxTokens: 1500,
    topP: 0.9,
    frequencyPenalty: 0.5,
    presencePenalty: 0.3
  },
  balanced: {
    temperature: 0.7,
    maxTokens: 1000,
    topP: 0.9,
    frequencyPenalty: 0,
    presencePenalty: 0
  },
  precise: {
    temperature: 0.2,
    maxTokens: 800,
    topP: 0.8,
    frequencyPenalty: 0,
    presencePenalty: 0
  }
};

export const PROMPT_TEMPLATES: PromptTemplate[] = [
  {
    id: 'creative-story',
    name: 'Creative Story Writer',
    description: 'Generate creative stories with rich descriptions',
    category: 'creative',
    content: 'Write a creative story about {topic}. Include vivid descriptions, engaging dialogue, and a compelling plot. The story should be approximately {length} words long and written in {style} style.',
    parameters: PARAMETER_PRESETS.creative,
    createdAt: '2024-01-15'
  },
  {
    id: 'code-reviewer',
    name: 'Code Review Assistant',
    description: 'Review code for best practices and improvements',
    category: 'technical',
    content: 'Please review the following code and provide feedback on:\n1. Code quality and readability\n2. Performance optimizations\n3. Security considerations\n4. Best practices adherence\n5. Suggested improvements\n\nCode:\n```\n{code}\n```',
    parameters: PARAMETER_PRESETS.precise,
    createdAt: '2024-01-15'
  },
  {
    id: 'data-analyst',
    name: 'Data Analysis Expert',
    description: 'Analyze data and provide insights',
    category: 'analysis',
    content: 'Analyze the following data and provide:\n1. Key insights and patterns\n2. Statistical summary\n3. Recommendations based on findings\n4. Potential areas for further investigation\n\nData: {data}\n\nPlease be thorough in your analysis and explain your reasoning.',
    parameters: PARAMETER_PRESETS.precise,
    createdAt: '2024-01-15'
  },
  {
    id: 'summarizer',
    name: 'Text Summarizer',
    description: 'Summarize long text content effectively',
    category: 'analysis',
    content: 'Please provide a comprehensive summary of the following text. Include:\n1. Main points and key arguments\n2. Important details and supporting evidence\n3. Conclusions or recommendations\n4. Any notable insights\n\nText to summarize:\n{text}',
    parameters: PARAMETER_PRESETS.balanced,
    createdAt: '2024-01-15'
  },
  {
    id: 'brainstorm',
    name: 'Brainstorming Assistant',
    description: 'Generate creative ideas and solutions',
    category: 'creative',
    content: 'Help me brainstorm ideas for: {topic}\n\nPlease provide:\n1. 10-15 creative and diverse ideas\n2. Brief explanations for each idea\n3. Potential challenges or considerations\n4. Next steps for the most promising ideas',
    parameters: PARAMETER_PRESETS.creative,
    createdAt: '2024-01-15'
  }
];