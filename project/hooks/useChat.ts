'use client';

import { useState, useCallback } from 'react';
import { ChatMessage, Conversation, ModelParameters } from '@/types';
import { useLocalStorage } from './useLocalStorage';
import { AI_MODELS } from '@/lib/constants';

export function useChat() {
  const [conversations, setConversations] = useLocalStorage<Conversation[]>('ai-chat-conversations', []);
  const [activeConversationId, setActiveConversationId] = useLocalStorage<string | null>('active-conversation', null);
  const [isStreaming, setIsStreaming] = useState(false);

  const activeConversation = conversations.find(c => c.id === activeConversationId);

  const createConversation = useCallback((model: string, parameters: ModelParameters) => {
    const newConversation: Conversation = {
      id: Date.now().toString(),
      title: 'New Conversation',
      messages: [],
      model,
      parameters,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    setConversations(prev => [newConversation, ...prev]);
    setActiveConversationId(newConversation.id);
    return newConversation;
  }, [setConversations, setActiveConversationId]);

  const updateConversation = useCallback((conversationId: string, updates: Partial<Conversation>) => {
    setConversations(prev => prev.map(conv => 
      conv.id === conversationId 
        ? { ...conv, ...updates, updatedAt: new Date().toISOString() }
        : conv
    ));
  }, [setConversations]);

  const deleteConversation = useCallback((conversationId: string) => {
    setConversations(prev => prev.filter(conv => conv.id !== conversationId));
    if (activeConversationId === conversationId) {
      setActiveConversationId(null);
    }
  }, [setConversations, activeConversationId, setActiveConversationId]);

  const addMessage = useCallback((conversationId: string, message: Omit<ChatMessage, 'id' | 'timestamp'>) => {
    const newMessage: ChatMessage = {
      ...message,
      id: Date.now().toString(),
      timestamp: new Date().toISOString()
    };

    setConversations(prev => prev.map(conv => {
      if (conv.id === conversationId) {
        const updatedMessages = [...conv.messages, newMessage];
        // Update title based on first user message
        const title = conv.title === 'New Conversation' && message.role === 'user' 
          ? message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '')
          : conv.title;
        
        return {
          ...conv,
          messages: updatedMessages,
          title,
          updatedAt: new Date().toISOString()
        };
      }
      return conv;
    }));

    return newMessage;
  }, [setConversations]);

  const simulateResponse = useCallback(async (
    conversationId: string, 
    userMessage: string, 
    model: string,
    onStreamChunk?: (chunk: string) => void
  ) => {
    setIsStreaming(true);

    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500 + Math.random() * 1000));

    const modelData = AI_MODELS.find(m => m.id === model);
    const responses = [
      "I understand your question. Let me provide a comprehensive response that addresses the key points you've raised. This is a simulated AI response that demonstrates the streaming capability of the interface.",
      "That's an interesting perspective. Based on the information you've provided, I can offer several insights and recommendations. The streaming effect you're seeing simulates how real AI responses would appear in production.",
      "Thank you for that query. I'll analyze the context and provide a detailed response. This simulation includes realistic delays and varied response lengths to demonstrate the user experience.",
      "Let me break this down for you step by step. The interface you're using showcases modern AI chat capabilities with proper message handling, history management, and export functionality.",
      "Great question! I can see you're exploring the features of this AI interface. The response streaming, parameter controls, and model selection all work together to create a professional AI chat experience."
    ];

    const response = responses[Math.floor(Math.random() * responses.length)];
    const chunks = response.split(' ');
    let accumulatedText = '';

    // Add assistant message placeholder
    const messageId = Date.now().toString();
    const assistantMessage: ChatMessage = {
      id: messageId,
      role: 'assistant',
      content: '',
      timestamp: new Date().toISOString(),
      tokens: Math.floor(response.length / 4),
      model: modelData?.name || model
    };

    setConversations(prev => prev.map(conv => {
      if (conv.id === conversationId) {
        return {
          ...conv,
          messages: [...conv.messages, assistantMessage],
          updatedAt: new Date().toISOString()
        };
      }
      return conv;
    }));

    // Stream the response
    for (let i = 0; i < chunks.length; i++) {
      await new Promise(resolve => setTimeout(resolve, 50 + Math.random() * 100));
      
      accumulatedText += (i === 0 ? '' : ' ') + chunks[i];
      
      setConversations(prev => prev.map(conv => {
        if (conv.id === conversationId) {
          return {
            ...conv,
            messages: conv.messages.map(msg => 
              msg.id === messageId 
                ? { ...msg, content: accumulatedText }
                : msg
            ),
            updatedAt: new Date().toISOString()
          };
        }
        return conv;
      }));

      onStreamChunk?.(chunks[i]);
    }

    setIsStreaming(false);
  }, [setConversations]);

  const exportConversation = useCallback((conversationId: string, format: 'json' | 'txt' | 'markdown') => {
    const conversation = conversations.find(c => c.id === conversationId);
    if (!conversation) return;

    let content: string;
    let filename: string;
    let mimeType: string;

    switch (format) {
      case 'json':
        content = JSON.stringify(conversation, null, 2);
        filename = `conversation-${conversation.id}.json`;
        mimeType = 'application/json';
        break;
      case 'txt':
        content = `Conversation: ${conversation.title}\nModel: ${conversation.model}\nCreated: ${new Date(conversation.createdAt).toLocaleString()}\n\n${conversation.messages.map(m => `${m.role.toUpperCase()}: ${m.content}`).join('\n\n')}`;
        filename = `conversation-${conversation.id}.txt`;
        mimeType = 'text/plain';
        break;
      case 'markdown':
        content = `# ${conversation.title}\n\n**Model:** ${conversation.model}  \n**Created:** ${new Date(conversation.createdAt).toLocaleString()}\n\n${conversation.messages.map(m => `## ${m.role === 'user' ? 'User' : 'Assistant'}\n\n${m.content}\n`).join('\n')}`;
        filename = `conversation-${conversation.id}.md`;
        mimeType = 'text/markdown';
        break;
    }

    const blob = new Blob([content], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  }, [conversations]);

  return {
    conversations,
    activeConversation,
    activeConversationId,
    isStreaming,
    setActiveConversationId,
    createConversation,
    updateConversation,
    deleteConversation,
    addMessage,
    simulateResponse,
    exportConversation
  };
}