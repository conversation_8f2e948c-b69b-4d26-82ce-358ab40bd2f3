'use client';

import { useState } from 'react';
import { Header } from './Header';
import { Sidebar } from './Sidebar';
import { ChatInterface } from '../features/ChatInterface';
import { ParametersPanel } from '../features/ParametersPanel';
import { PromptEditor } from '../features/PromptEditor';
import { useChat } from '@/hooks/useChat';
import { AI_MODELS, DEFAULT_PARAMETERS } from '@/lib/constants';
import { ModelParameters, PromptTemplate } from '@/types';
import { PanelRightClose, PanelRightOpen } from 'lucide-react';
import { Button } from '../ui/Button';
import { cn } from '@/lib/utils';

export function MainLayout() {
  const [selectedModel, setSelectedModel] = useState('gpt-4');
  const [parameters, setParameters] = useState<ModelParameters>(DEFAULT_PARAMETERS);
  const [promptValue, setPromptValue] = useState('');
  const [isParametersPanelOpen, setIsParametersPanelOpen] = useState(true);

  const { 
    activeConversation, 
    isStreaming, 
    createConversation, 
    addMessage, 
    simulateResponse 
  } = useChat();

  const handleSendMessage = async (message: string) => {
    let conversationId = activeConversation?.id;
    
    if (!conversationId) {
      const newConversation = createConversation(selectedModel, parameters);
      conversationId = newConversation.id;
    }

    // Add user message
    addMessage(conversationId, {
      role: 'user',
      content: message,
      tokens: Math.ceil(message.length / 4)
    });

    // Clear prompt
    setPromptValue('');

    // Simulate AI response
    await simulateResponse(conversationId, message, selectedModel);
  };

  const handleTemplateSelect = (template: PromptTemplate) => {
    setPromptValue(template.content);
    setParameters(template.parameters);
  };

  const messages = activeConversation?.messages || [];

  return (
    <div className="h-screen flex flex-col bg-slate-50 dark:bg-slate-950">
      <Header />
      
      <div className="flex-1 flex overflow-hidden">
        <Sidebar
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
          onTemplateSelect={handleTemplateSelect}
        />
        
        <main className="flex-1 flex flex-col">
          <div className="flex-1 flex">
            <div className="flex-1 flex flex-col">
              <ChatInterface
                messages={messages}
                isStreaming={isStreaming}
                className="flex-1"
              />
              
              <div className="p-4 border-t border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900">
                <PromptEditor
                  value={promptValue}
                  onChange={setPromptValue}
                  onSend={handleSendMessage}
                  disabled={isStreaming}
                />
              </div>
            </div>

            <div className={cn(
              'transition-all duration-300',
              isParametersPanelOpen ? 'w-80' : 'w-12'
            )}>
              {isParametersPanelOpen ? (
                <ParametersPanel
                  parameters={parameters}
                  onParametersChange={setParameters}
                  selectedModel={selectedModel}
                />
              ) : (
                <div className="p-4 border-l border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsParametersPanelOpen(true)}
                  >
                    <PanelRightOpen className="h-4 w-4" />
                  </Button>
                </div>
              )}
              
              {isParametersPanelOpen && (
                <div className="absolute top-4 right-4">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsParametersPanelOpen(false)}
                  >
                    <PanelRightClose className="h-4 w-4" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
}