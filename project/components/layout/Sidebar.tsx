'use client';

import { useState } from 'react';
import { PanelLeftClose, PanelLeftOpen } from 'lucide-react';
import { ModelSelector } from '../features/ModelSelector';
import { ChatHistory } from '../features/ChatHistory';
import { TemplateManager } from '../features/TemplateManager';
import { Button } from '../ui/Button';
import { cn } from '@/lib/utils';
import { useChat } from '@/hooks/useChat';
import { AI_MODELS, DEFAULT_PARAMETERS } from '@/lib/constants';
import { PromptTemplate } from '@/types';

interface SidebarProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  onTemplateSelect: (template: PromptTemplate) => void;
  className?: string;
}

export function Sidebar({ 
  selectedModel, 
  onModelChange, 
  onTemplateSelect, 
  className 
}: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const { 
    conversations, 
    activeConversationId, 
    setActiveConversationId, 
    createConversation, 
    deleteConversation,
    exportConversation 
  } = useChat();

  const handleNewConversation = () => {
    const model = AI_MODELS.find(m => m.id === selectedModel);
    if (model) {
      createConversation(selectedModel, DEFAULT_PARAMETERS);
    }
  };

  return (
    <aside className={cn(
      'border-r border-slate-200 dark:border-slate-700 bg-white dark:bg-slate-900 transition-all duration-300',
      isCollapsed ? 'w-16' : 'w-80',
      className
    )}>
      <div className="p-4">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="mb-4"
        >
          {isCollapsed ? (
            <PanelLeftOpen className="h-4 w-4" />
          ) : (
            <PanelLeftClose className="h-4 w-4" />
          )}
        </Button>

        {!isCollapsed && (
          <div className="space-y-6">
            <ModelSelector
              selectedModel={selectedModel}
              onModelChange={onModelChange}
            />

            <ChatHistory
              conversations={conversations}
              activeConversationId={activeConversationId}
              onSelectConversation={setActiveConversationId}
              onDeleteConversation={deleteConversation}
              onExportConversation={exportConversation}
              onNewConversation={handleNewConversation}
            />

            <TemplateManager
              onSelectTemplate={onTemplateSelect}
            />
          </div>
        )}
      </div>
    </aside>
  );
}