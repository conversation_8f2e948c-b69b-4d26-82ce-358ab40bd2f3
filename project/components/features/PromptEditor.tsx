'use client';

import { useState, useRef, useEffect } from 'react';
import { Button } from '../ui/Button';
import { Send, Save, Trash2, FileText } from 'lucide-react';
import { PromptTemplate } from '@/types';
import { PROMPT_TEMPLATES } from '@/lib/constants';
import { cn } from '@/lib/utils';

interface PromptEditorProps {
  value: string;
  onChange: (value: string) => void;
  onSend: (message: string) => void;
  disabled?: boolean;
  className?: string;
}

export function PromptEditor({ 
  value, 
  onChange, 
  onSend, 
  disabled = false,
  className 
}: PromptEditorProps) {
  const [showTemplates, setShowTemplates] = useState(false);
  const [tokenCount, setTokenCount] = useState(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea
  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = 'auto';
      textarea.style.height = Math.min(textarea.scrollHeight, 200) + 'px';
    }
  }, [value]);

  // Calculate approximate token count (rough estimation: 1 token ≈ 4 characters)
  useEffect(() => {
    setTokenCount(Math.ceil(value.length / 4));
  }, [value]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (value.trim() && !disabled) {
      onSend(value);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && (e.metaKey || e.ctrlKey) && !disabled) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const insertTemplate = (template: PromptTemplate) => {
    onChange(template.content);
    setShowTemplates(false);
  };

  const clearPrompt = () => {
    onChange('');
  };

  return (
    <div className={cn('space-y-3', className)}>
      <div className="relative">
        <textarea
          ref={textareaRef}
          value={value}
          onChange={(e) => onChange(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder="Type your message here... (Ctrl/Cmd + Enter to send)"
          disabled={disabled}
          className={cn(
            'w-full min-h-[80px] max-h-[200px] p-4 border border-slate-200 dark:border-slate-700 rounded-lg bg-white dark:bg-slate-950 text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 dark:focus:ring-offset-slate-900 resize-none transition-colors',
            disabled && 'opacity-50 cursor-not-allowed'
          )}
        />

        {/* Template dropdown */}
        {showTemplates && (
          <>
            <div 
              className="fixed inset-0 z-10" 
              onClick={() => setShowTemplates(false)}
            />
            <div className="absolute bottom-full left-0 right-0 z-20 mb-2 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg max-h-60 overflow-y-auto">
              <div className="p-3 border-b border-slate-100 dark:border-slate-800">
                <h4 className="font-medium text-slate-900 dark:text-slate-100">
                  Prompt Templates
                </h4>
                <p className="text-xs text-slate-500 dark:text-slate-400">
                  Click to insert a template
                </p>
              </div>
              {PROMPT_TEMPLATES.map((template) => (
                <button
                  key={template.id}
                  onClick={() => insertTemplate(template)}
                  className="w-full px-3 py-2 text-left hover:bg-slate-50 dark:hover:bg-slate-800 border-b border-slate-100 dark:border-slate-800 last:border-0"
                >
                  <div className="font-medium text-sm">{template.name}</div>
                  <div className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    {template.description}
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    {template.category}
                  </div>
                </button>
              ))}
            </div>
          </>
        )}
      </div>

      {/* Controls */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowTemplates(!showTemplates)}
            className="text-slate-600 dark:text-slate-400"
          >
            <FileText className="h-4 w-4 mr-1" />
            Templates
          </Button>
          
          {value.trim() && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearPrompt}
              className="text-slate-600 dark:text-slate-400"
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Clear
            </Button>
          )}
        </div>

        <div className="flex items-center gap-3">
          {/* Token count */}
          <div className="text-xs text-slate-500 dark:text-slate-400">
            {value.length} chars • ~{tokenCount} tokens
          </div>

          {/* Send button */}
          <Button
            onClick={handleSubmit}
            disabled={!value.trim() || disabled}
            size="sm"
            className="gap-1"
          >
            <Send className="h-4 w-4" />
            Send
          </Button>
        </div>
      </div>
    </div>
  );
}