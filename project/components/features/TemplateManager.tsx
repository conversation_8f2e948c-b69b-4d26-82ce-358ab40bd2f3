'use client';

import { useState } from 'react';
import { PromptTemplate } from '@/types';
import { PROMPT_TEMPLATES } from '@/lib/constants';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { FileText, Plus, Trash2, Edit } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TemplateManagerProps {
  onSelectTemplate: (template: PromptTemplate) => void;
  className?: string;
}

export function TemplateManager({ onSelectTemplate, className }: TemplateManagerProps) {
  const [templates, setTemplates] = useState<PromptTemplate[]>(PROMPT_TEMPLATES);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingTemplate, setEditingTemplate] = useState<PromptTemplate | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<string>('all');

  const categories = ['all', 'creative', 'technical', 'analysis', 'custom'];

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === selectedCategory);

  const openCreateModal = () => {
    setEditingTemplate(null);
    setIsModalOpen(true);
  };

  const openEditModal = (template: PromptTemplate) => {
    setEditingTemplate(template);
    setIsModalOpen(true);
  };

  const deleteTemplate = (templateId: string) => {
    setTemplates(prev => prev.filter(t => t.id !== templateId));
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-slate-600 dark:text-slate-400" />
          <h3 className="font-semibold text-slate-900 dark:text-slate-100">
            Templates
          </h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={openCreateModal}
          className="text-slate-600 dark:text-slate-400"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {/* Category filters */}
      <div className="flex flex-wrap gap-2">
        {categories.map(category => (
          <Button
            key={category}
            variant={selectedCategory === category ? 'default' : 'outline'}
            size="sm"
            onClick={() => setSelectedCategory(category)}
            className="text-xs capitalize"
          >
            {category}
          </Button>
        ))}
      </div>

      {/* Templates list */}
      <div className="space-y-2 max-h-96 overflow-y-auto">
        {filteredTemplates.map(template => (
          <div
            key={template.id}
            className="p-3 border border-slate-200 dark:border-slate-700 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-800 transition-colors group"
          >
            <div className="flex items-start justify-between">
              <button
                onClick={() => onSelectTemplate(template)}
                className="flex-1 text-left"
              >
                <div className="font-medium text-sm text-slate-900 dark:text-slate-100">
                  {template.name}
                </div>
                <div className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                  {template.description}
                </div>
                <div className="text-xs text-blue-600 dark:text-blue-400 mt-1 capitalize">
                  {template.category}
                </div>
              </button>
              
              <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => openEditModal(template)}
                  className="h-6 w-6 p-0"
                >
                  <Edit className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => deleteTemplate(template.id)}
                  className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Template creation/editing modal */}
      <TemplateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        template={editingTemplate}
        onSave={(template) => {
          if (editingTemplate) {
            setTemplates(prev => prev.map(t => 
              t.id === editingTemplate.id ? template : t
            ));
          } else {
            setTemplates(prev => [template, ...prev]);
          }
          setIsModalOpen(false);
        }}
      />
    </div>
  );
}

interface TemplateModalProps {
  isOpen: boolean;
  onClose: () => void;
  template: PromptTemplate | null;
  onSave: (template: PromptTemplate) => void;
}

function TemplateModal({ isOpen, onClose, template, onSave }: TemplateModalProps) {
  const [formData, setFormData] = useState({
    name: template?.name || '',
    description: template?.description || '',
    category: template?.category || 'custom' as const,
    content: template?.content || ''
  });

  const handleSave = () => {
    const newTemplate: PromptTemplate = {
      id: template?.id || Date.now().toString(),
      ...formData,
      parameters: template?.parameters || {
        temperature: 0.7,
        maxTokens: 1000,
        topP: 0.9,
        frequencyPenalty: 0,
        presencePenalty: 0
      },
      createdAt: template?.createdAt || new Date().toISOString()
    };

    onSave(newTemplate);
    setFormData({
      name: '',
      description: '',
      category: 'custom',
      content: ''
    });
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={template ? 'Edit Template' : 'Create Template'}
      size="lg"
    >
      <div className="p-6 space-y-4">
        <Input
          placeholder="Template name"
          value={formData.name}
          onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
        />
        
        <Input
          placeholder="Description"
          value={formData.description}
          onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
        />

        <select
          value={formData.category}
          onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value as any }))}
          className="w-full h-10 px-3 border border-slate-200 dark:border-slate-700 rounded-lg bg-white dark:bg-slate-950 text-slate-900 dark:text-slate-100"
        >
          <option value="creative">Creative</option>
          <option value="technical">Technical</option>
          <option value="analysis">Analysis</option>
          <option value="custom">Custom</option>
        </select>

        <textarea
          placeholder="Template content (use {variable} for placeholders)"
          value={formData.content}
          onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
          rows={6}
          className="w-full p-3 border border-slate-200 dark:border-slate-700 rounded-lg bg-white dark:bg-slate-950 text-slate-900 dark:text-slate-100 placeholder:text-slate-500 dark:placeholder:text-slate-400 resize-none focus:outline-none focus:ring-2 focus:ring-blue-500"
        />

        <div className="flex justify-end gap-2 pt-4 border-t border-slate-200 dark:border-slate-700">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={!formData.name || !formData.content}
          >
            {template ? 'Update' : 'Create'}
          </Button>
        </div>
      </div>
    </Modal>
  );
}