'use client';

import { ModelParameters } from '@/types';
import { PARAMETER_PRESETS, AI_MODELS } from '@/lib/constants';
import { Slider } from '../ui/Slider';
import { Button } from '../ui/Button';
import { RotateCcw, Settings } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ParametersPanelProps {
  parameters: ModelParameters;
  onParametersChange: (parameters: ModelParameters) => void;
  selectedModel: string;
  className?: string;
}

export function ParametersPanel({ 
  parameters, 
  onParametersChange, 
  selectedModel,
  className 
}: ParametersPanelProps) {
  const model = AI_MODELS.find(m => m.id === selectedModel);
  const maxTokens = model?.maxTokens || 4096;

  const handleParameterChange = (key: keyof ModelParameters, value: number) => {
    onParametersChange({
      ...parameters,
      [key]: value
    });
  };

  const applyPreset = (preset: keyof typeof PARAMETER_PRESETS) => {
    const presetParams = { ...PARAMETER_PRESETS[preset] };
    // Ensure max tokens doesn't exceed model limit
    presetParams.maxTokens = Math.min(presetParams.maxTokens, maxTokens);
    onParametersChange(presetParams);
  };

  const resetToDefaults = () => {
    onParametersChange({
      temperature: 0.7,
      maxTokens: Math.min(1000, maxTokens),
      topP: 0.9,
      frequencyPenalty: 0,
      presencePenalty: 0
    });
  };

  const estimatedCost = model ? (parameters.maxTokens / 1000) * model.costPer1kTokens : 0;

  return (
    <div className={cn('space-y-6 p-6 bg-white dark:bg-slate-900 border-l border-slate-200 dark:border-slate-700', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Settings className="h-5 w-5 text-slate-600 dark:text-slate-400" />
          <h3 className="font-semibold text-slate-900 dark:text-slate-100">
            Parameters
          </h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={resetToDefaults}
          className="text-slate-500 hover:text-slate-700"
        >
          <RotateCcw className="h-4 w-4" />
        </Button>
      </div>

      {/* Presets */}
      <div>
        <label className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2 block">
          Presets
        </label>
        <div className="grid grid-cols-3 gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyPreset('creative')}
            className="text-xs"
          >
            Creative
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyPreset('balanced')}
            className="text-xs"
          >
            Balanced
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => applyPreset('precise')}
            className="text-xs"
          >
            Precise
          </Button>
        </div>
      </div>

      {/* Temperature */}
      <Slider
        label="Temperature"
        value={parameters.temperature}
        onChange={(value) => handleParameterChange('temperature', value)}
        min={0}
        max={2}
        step={0.1}
      />

      {/* Max Tokens */}
      <Slider
        label="Max Tokens"
        value={parameters.maxTokens}
        onChange={(value) => handleParameterChange('maxTokens', value)}
        min={1}
        max={maxTokens}
        step={1}
      />

      {/* Top P */}
      <Slider
        label="Top P"
        value={parameters.topP}
        onChange={(value) => handleParameterChange('topP', value)}
        min={0}
        max={1}
        step={0.05}
      />

      {/* Frequency Penalty */}
      <Slider
        label="Frequency Penalty"
        value={parameters.frequencyPenalty}
        onChange={(value) => handleParameterChange('frequencyPenalty', value)}
        min={-2}
        max={2}
        step={0.1}
      />

      {/* Presence Penalty */}
      <Slider
        label="Presence Penalty"
        value={parameters.presencePenalty}
        onChange={(value) => handleParameterChange('presencePenalty', value)}
        min={-2}
        max={2}
        step={0.1}
      />

      {/* Cost Estimation */}
      {model && model.costPer1kTokens > 0 && (
        <div className="pt-4 border-t border-slate-200 dark:border-slate-700">
          <div className="text-sm text-slate-600 dark:text-slate-400">
            Estimated cost per request:
          </div>
          <div className="font-mono text-sm font-medium text-green-600 dark:text-green-400">
            ${estimatedCost.toFixed(4)}
          </div>
        </div>
      )}

      {/* Parameter Info */}
      <div className="text-xs text-slate-500 dark:text-slate-400 space-y-1">
        <div><strong>Temperature:</strong> Controls randomness (0 = focused, 2 = creative)</div>
        <div><strong>Top P:</strong> Nucleus sampling threshold</div>
        <div><strong>Frequency Penalty:</strong> Reduces repetition</div>
      </div>
    </div>
  );
}