'use client';

import { <PERSON>, <PERSON>, <PERSON> } from 'lucide-react';
import { useTheme } from '@/hooks/useTheme';
import { Button } from '../ui/Button';

export function ThemeToggle() {
  const { theme, setTheme } = useTheme();

  const themes = [
    { value: 'light' as const, icon: Sun, label: 'Light' },
    { value: 'dark' as const, icon: Moon, label: 'Dark' },
    { value: 'system' as const, icon: Monitor, label: 'System' }
  ];

  return (
    <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-lg p-1 border border-white/30">
      {themes.map(({ value, icon: Icon, label }) => (
        <Button
          key={value}
          variant={theme === value ? 'default' : 'ghost'}
          size="sm"
          onClick={() => setTheme(value)}
          className={`h-8 w-8 p-0 ${
            theme === value
              ? 'bg-white text-[#7C3AED] hover:bg-white/90'
              : 'text-white hover:bg-white/20'
          }`}
          title={label}
        >
          <Icon className="h-4 w-4" />
        </Button>
      ))}
    </div>
  );
}