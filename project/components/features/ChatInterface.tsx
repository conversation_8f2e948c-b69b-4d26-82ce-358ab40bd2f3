'use client';

import { useEffect, useRef, useCallback, useState } from 'react';
import { ChatMessage } from '@/types';
import { Button } from '../ui/Button';
import { Copy, RotateCcw, User, Bot, ChevronDown } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInterfaceProps {
  messages: ChatMessage[];
  isStreaming: boolean;
  className?: string;
}

export function ChatInterface({ messages, isStreaming, className }: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const chatContainerRef = useRef<HTMLDivElement>(null);
  const [showScrollToBottom, setShowScrollToBottom] = useState(false);
  const [isUserScrolling, setIsUserScrolling] = useState(false);
  const [isNearBottom, setIsNearBottom] = useState(true);
  const scrollTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const lastMessageCountRef = useRef(messages.length);

  const scrollToBottom = useCallback(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'end',
        inline: 'nearest'
      });
    }
  }, []);

  // Check if user is near the bottom of the chat
  const checkScrollPosition = useCallback(() => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } = chatContainerRef.current;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      // Consider user at bottom if within 100px or if they've scrolled to the very bottom
      const nearBottom = distanceFromBottom < 100 || Math.abs(distanceFromBottom) < 1;

      setIsNearBottom(nearBottom);
      setShowScrollToBottom(!nearBottom);
    }
  }, []);

  // Handle scroll events with debouncing
  const handleScroll = useCallback(() => {
    setIsUserScrolling(true);
    checkScrollPosition();

    // Clear existing timeout
    if (scrollTimeoutRef.current) {
      clearTimeout(scrollTimeoutRef.current);
    }

    // Set new timeout to reset user scrolling flag
    scrollTimeoutRef.current = setTimeout(() => {
      setIsUserScrolling(false);
    }, 1000); // Increased timeout to 1 second
  }, [checkScrollPosition]);

  // Auto-scroll to bottom only when:
  // 1. New messages are added AND user was near bottom
  // 2. User is not actively scrolling
  // 3. Streaming starts (only if user was near bottom)
  useEffect(() => {
    const hasNewMessages = messages.length > lastMessageCountRef.current;
    lastMessageCountRef.current = messages.length;

    if (hasNewMessages && isNearBottom && !isUserScrolling) {
      // Small delay to ensure DOM is updated
      setTimeout(() => {
        scrollToBottom();
      }, 50);
    }
  }, [messages.length, scrollToBottom, isNearBottom, isUserScrolling]);

  // Handle streaming auto-scroll
  useEffect(() => {
    if (isStreaming && isNearBottom && !isUserScrolling) {
      scrollToBottom();
    }
  }, [isStreaming, scrollToBottom, isNearBottom, isUserScrolling]);

  // Add scroll event listener
  useEffect(() => {
    const container = chatContainerRef.current;
    if (container) {
      container.addEventListener('scroll', handleScroll, { passive: true });
      // Initial scroll position check
      checkScrollPosition();

      return () => {
        container.removeEventListener('scroll', handleScroll);
        if (scrollTimeoutRef.current) {
          clearTimeout(scrollTimeoutRef.current);
        }
      };
    }
  }, [handleScroll, checkScrollPosition]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (messages.length === 0) {
    return (
      <div className={cn(
        'flex-1 flex items-center justify-center bg-slate-50 dark:bg-slate-950',
        className
      )}>
        <div className="text-center space-y-4 max-w-md">
          <div className="text-6xl">🤖</div>
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
            Welcome to AI Chat
          </h2>
          <p className="text-slate-600 dark:text-slate-400">
            Start a conversation by typing a message below. Choose your model and adjust parameters to customize the AI's responses.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex-1 relative', className)}>
      <div
        ref={chatContainerRef}
        className={cn(
          'absolute inset-0 overflow-y-auto overflow-x-hidden',
          'chat-scroll-container'
        )}
      >
        <div className="space-y-6 p-6 min-h-full flex flex-col">
          <div className="flex-1">
            {messages.map((message) => (
              <div
                key={message.id}
                className={cn(
                  'flex gap-4 mb-6',
                  message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
                )}
              >
                {/* Avatar */}
                <div className={cn(
                  'w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0',
                  message.role === 'user'
                    ? 'bg-blue-600 text-white'
                    : 'bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300'
                )}>
                  {message.role === 'user' ? (
                    <User className="h-4 w-4" />
                  ) : (
                    <Bot className="h-4 w-4" />
                  )}
                </div>

                {/* Message bubble */}
                <div className={cn(
                  'flex-1 max-w-3xl',
                  message.role === 'user' ? 'text-right' : 'text-left'
                )}>
                  <div className={cn(
                    'inline-block p-4 rounded-lg',
                    message.role === 'user'
                      ? 'bg-blue-600 text-white'
                      : 'bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700'
                  )}>
                    <div className="whitespace-pre-wrap break-words">
                      {message.content}
                    </div>
                  </div>

                  {/* Message footer */}
                  <div className={cn(
                    'flex items-center gap-2 mt-2 text-xs text-slate-500 dark:text-slate-400',
                    message.role === 'user' ? 'justify-end' : 'justify-start'
                  )}>
                    <span>{formatTimestamp(message.timestamp)}</span>
                    {message.tokens && (
                      <span>• {message.tokens} tokens</span>
                    )}
                    {message.model && (
                      <span>• {message.model}</span>
                    )}

                    {/* Action buttons */}
                    <div className="flex items-center gap-1 ml-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => copyToClipboard(message.content)}
                        className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
                      >
                        <Copy className="h-3 w-3" />
                      </Button>

                      {message.role === 'assistant' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
                        >
                          <RotateCcw className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            ))}

            {/* Streaming indicator */}
            {isStreaming && (
              <div className="flex gap-4 mb-6">
                <div className="w-8 h-8 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 flex items-center justify-center flex-shrink-0">
                  <Bot className="h-4 w-4" />
                </div>
                <div className="flex-1">
                  <div className="inline-block p-4 rounded-lg bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
                    <div className="flex items-center gap-2">
                      <div className="flex space-x-1">
                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-pulse"></div>
                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                        <div className="w-2 h-2 bg-slate-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                      </div>
                      <span className="text-sm text-slate-500 dark:text-slate-400">
                        AI is thinking...
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Scroll anchor for auto-scroll to bottom */}
          <div
            ref={messagesEndRef}
            className="h-1 flex-shrink-0"
            aria-hidden="true"
          />
        </div>
      </div>

      {/* Scroll to bottom button */}
      {showScrollToBottom && (
        <div className="absolute bottom-4 right-4 z-10">
          <Button
            onClick={() => {
              setIsUserScrolling(false); // Reset user scrolling state
              scrollToBottom();
            }}
            className={cn(
              'h-10 w-10 rounded-full shadow-lg',
              'bg-blue-600 hover:bg-blue-700 text-white',
              'transition-all duration-200 ease-in-out',
              'animate-in slide-in-from-bottom-2'
            )}
            size="sm"
            aria-label="Scroll to bottom"
          >
            <ChevronDown className="h-4 w-4" />
          </Button>
        </div>
      )}
    </div>
  );
}