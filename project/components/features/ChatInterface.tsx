'use client';

import { useEffect, useRef } from 'react';
import { ChatMessage } from '@/types';
import { Button } from '../ui/Button';
import { Co<PERSON>, RotateCcw, User, Bot } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatInterfaceProps {
  messages: ChatMessage[];
  isStreaming: boolean;
  className?: string;
}

export function ChatInterface({ messages, isStreaming, className }: ChatInterfaceProps) {
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages, isStreaming]);

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    // You could add a toast notification here
  };

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  if (messages.length === 0) {
    return (
      <div className={cn(
        'flex-1 flex items-center justify-center bg-slate-50 dark:bg-slate-950',
        className
      )}>
        <div className="text-center space-y-4 max-w-md">
          <div className="text-6xl">🤖</div>
          <h2 className="text-2xl font-semibold text-slate-900 dark:text-slate-100">
            Welcome to AI Chat
          </h2>
          <p className="text-slate-600 dark:text-slate-400">
            Start a conversation by typing a message below. Choose your model and adjust parameters to customize the AI's responses.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('flex-1 overflow-y-auto', className)}>
      <div className="space-y-6 p-6">
        {messages.map((message) => (
          <div
            key={message.id}
            className={cn(
              'flex gap-4',
              message.role === 'user' ? 'flex-row-reverse' : 'flex-row'
            )}
          >
            {/* Avatar */}
            <div className={cn(
              'w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0',
              message.role === 'user' 
                ? 'bg-blue-600 text-white' 
                : 'bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300'
            )}>
              {message.role === 'user' ? (
                <User className="h-4 w-4" />
              ) : (
                <Bot className="h-4 w-4" />
              )}
            </div>

            {/* Message bubble */}
            <div className={cn(
              'flex-1 max-w-3xl',
              message.role === 'user' ? 'text-right' : 'text-left'
            )}>
              <div className={cn(
                'inline-block p-4 rounded-lg',
                message.role === 'user'
                  ? 'bg-blue-600 text-white'
                  : 'bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700'
              )}>
                <div className="whitespace-pre-wrap break-words">
                  {message.content}
                </div>
              </div>

              {/* Message footer */}
              <div className={cn(
                'flex items-center gap-2 mt-2 text-xs text-slate-500 dark:text-slate-400',
                message.role === 'user' ? 'justify-end' : 'justify-start'
              )}>
                <span>{formatTimestamp(message.timestamp)}</span>
                {message.tokens && (
                  <span>• {message.tokens} tokens</span>
                )}
                {message.model && (
                  <span>• {message.model}</span>
                )}
                
                {/* Action buttons */}
                <div className="flex items-center gap-1 ml-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => copyToClipboard(message.content)}
                    className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
                  >
                    <Copy className="h-3 w-3" />
                  </Button>
                  
                  {message.role === 'assistant' && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0 opacity-50 hover:opacity-100"
                    >
                      <RotateCcw className="h-3 w-3" />
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}

        {/* Streaming indicator */}
        {isStreaming && (
          <div className="flex gap-4">
            <div className="w-8 h-8 rounded-full bg-slate-200 dark:bg-slate-700 text-slate-600 dark:text-slate-300 flex items-center justify-center flex-shrink-0">
              <Bot className="h-4 w-4" />
            </div>
            <div className="flex-1">
              <div className="inline-block p-4 rounded-lg bg-white dark:bg-slate-800 border border-slate-200 dark:border-slate-700">
                <div className="flex items-center gap-2">
                  <div className="flex space-x-1">
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-pulse"></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-pulse" style={{ animationDelay: '0.2s' }}></div>
                    <div className="w-2 h-2 bg-slate-400 rounded-full animate-pulse" style={{ animationDelay: '0.4s' }}></div>
                  </div>
                  <span className="text-sm text-slate-500 dark:text-slate-400">
                    AI is thinking...
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
      <div ref={messagesEndRef} />
    </div>
  );
}