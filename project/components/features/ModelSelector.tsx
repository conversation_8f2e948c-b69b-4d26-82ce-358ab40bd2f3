'use client';

import { useState } from 'react';
import { ChevronDown, Check } from 'lucide-react';
import { AIModel } from '@/types';
import { AI_MODELS } from '@/lib/constants';
import { cn } from '@/lib/utils';
import { Button } from '../ui/Button';

interface ModelSelectorProps {
  selectedModel: string;
  onModelChange: (modelId: string) => void;
  className?: string;
}

export function ModelSelector({ selectedModel, onModelChange, className }: ModelSelectorProps) {
  const [isOpen, setIsOpen] = useState(false);
  
  const selected = AI_MODELS.find(model => model.id === selectedModel);

  return (
    <div className={cn('relative', className)}>
      <Button
        variant="outline"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full justify-between h-12 text-left"
      >
        <div className="flex items-center gap-3">
          <span className="text-xl">{selected?.icon}</span>
          <div className="flex flex-col">
            <span className="font-medium">{selected?.name}</span>
            <span className="text-xs text-slate-500 dark:text-slate-400">
              {selected?.provider}
            </span>
          </div>
        </div>
        <ChevronDown className={cn(
          'h-4 w-4 transition-transform',
          isOpen && 'rotate-180'
        )} />
      </Button>

      {isOpen && (
        <>
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setIsOpen(false)}
          />
          <div className="absolute top-full left-0 right-0 z-20 mt-1 bg-white dark:bg-slate-900 border border-slate-200 dark:border-slate-700 rounded-lg shadow-lg max-h-60 overflow-y-auto">
            {AI_MODELS.map((model) => (
              <button
                key={model.id}
                onClick={() => {
                  onModelChange(model.id);
                  setIsOpen(false);
                }}
                className="w-full px-4 py-3 text-left hover:bg-slate-50 dark:hover:bg-slate-800 flex items-center gap-3 border-b border-slate-100 dark:border-slate-800 last:border-0"
              >
                <span className="text-lg">{model.icon}</span>
                <div className="flex-1">
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{model.name}</span>
                    {selectedModel === model.id && (
                      <Check className="h-4 w-4 text-blue-600" />
                    )}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400">
                    {model.provider} • Max {model.maxTokens} tokens
                  </div>
                  <div className="text-xs text-slate-400 dark:text-slate-500 mt-1">
                    {model.description}
                  </div>
                </div>
              </button>
            ))}
          </div>
        </>
      )}
    </div>
  );
}