'use client';

import { useState } from 'react';
import { Conversation } from '@/types';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { MessageSquare, Trash2, Download, Plus } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ChatHistoryProps {
  conversations: Conversation[];
  activeConversationId: string | null;
  onSelectConversation: (conversationId: string) => void;
  onDeleteConversation: (conversationId: string) => void;
  onExportConversation: (conversationId: string, format: 'json' | 'txt' | 'markdown') => void;
  onNewConversation: () => void;
  className?: string;
}

export function ChatHistory({ 
  conversations, 
  activeConversationId, 
  onSelectConversation,
  onDeleteConversation,
  onExportConversation,
  onNewConversation,
  className 
}: ChatHistoryProps) {
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [selectedConversation, setSelectedConversation] = useState<string | null>(null);

  const openExportModal = (conversationId: string) => {
    setSelectedConversation(conversationId);
    setExportModalOpen(true);
  };

  const handleExport = (format: 'json' | 'txt' | 'markdown') => {
    if (selectedConversation) {
      onExportConversation(selectedConversation, format);
      setExportModalOpen(false);
      setSelectedConversation(null);
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffTime = Math.abs(now.getTime() - date.getTime());
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    if (diffDays === 1) return 'Today';
    if (diffDays === 2) return 'Yesterday';
    if (diffDays <= 7) return `${diffDays - 1} days ago`;
    
    return date.toLocaleDateString();
  };

  return (
    <div className={cn('space-y-4', className)}>
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <MessageSquare className="h-5 w-5 text-slate-600 dark:text-slate-400" />
          <h3 className="font-semibold text-slate-900 dark:text-slate-100">
            Chat History
          </h3>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={onNewConversation}
          className="text-slate-600 dark:text-slate-400"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      <div className="space-y-2 max-h-96 overflow-y-auto">
        {conversations.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-8 w-8 text-slate-400 mx-auto mb-2" />
            <p className="text-sm text-slate-500 dark:text-slate-400">
              No conversations yet
            </p>
            <Button
              variant="ghost"
              size="sm"
              onClick={onNewConversation}
              className="mt-2"
            >
              Start your first chat
            </Button>
          </div>
        ) : (
          conversations.map(conversation => (
            <div
              key={conversation.id}
              className={cn(
                'p-3 border rounded-lg transition-colors group cursor-pointer',
                activeConversationId === conversation.id
                  ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/20'
                  : 'border-slate-200 dark:border-slate-700 hover:bg-slate-50 dark:hover:bg-slate-800'
              )}
              onClick={() => onSelectConversation(conversation.id)}
            >
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm text-slate-900 dark:text-slate-100 truncate">
                    {conversation.title}
                  </div>
                  <div className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                    {conversation.messages.length} messages • {formatDate(conversation.updatedAt)}
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                    {conversation.model}
                  </div>
                </div>
                
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      openExportModal(conversation.id);
                    }}
                    className="h-6 w-6 p-0"
                  >
                    <Download className="h-3 w-3" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDeleteConversation(conversation.id);
                    }}
                    className="h-6 w-6 p-0 text-red-600 hover:text-red-700"
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Export Modal */}
      <Modal
        isOpen={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
        title="Export Conversation"
        size="sm"
      >
        <div className="p-6 space-y-4">
          <p className="text-sm text-slate-600 dark:text-slate-400">
            Choose the format to export your conversation:
          </p>
          
          <div className="space-y-2">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleExport('json')}
            >
              <div className="text-left">
                <div className="font-medium">JSON</div>
                <div className="text-xs text-slate-500">Complete data with metadata</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleExport('txt')}
            >
              <div className="text-left">
                <div className="font-medium">Text</div>
                <div className="text-xs text-slate-500">Plain text format</div>
              </div>
            </Button>
            
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => handleExport('markdown')}
            >
              <div className="text-left">
                <div className="font-medium">Markdown</div>
                <div className="text-xs text-slate-500">Formatted for documentation</div>
              </div>
            </Button>
          </div>
        </div>
      </Modal>
    </div>
  );
}