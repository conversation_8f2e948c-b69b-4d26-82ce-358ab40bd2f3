'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { ThemeToggle } from '@/components/features/ThemeToggle';
import { useTheme } from '@/hooks/useTheme';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export function LandingPage() {
  const router = useRouter();
  const { resolvedTheme } = useTheme();
  const [activeNav, setActiveNav] = useState('Home');

  const navItems = ['Home', 'Pricing', 'Features', 'Contact'];

  const handleGetStarted = () => {
    router.push('/chat');
  };

  const handleRegister = () => {
    // You can implement registration logic here
    router.push('/chat');
  };

  // Theme-aware colors
  const isDark = resolvedTheme === 'dark';

  return (
    <div className="min-h-screen overflow-hidden relative transition-colors duration-300">
      {/* Background - Strong purple gradient matching Figma */}
      <div
        className="absolute inset-0 w-full h-full"
        style={{
          background: isDark
            ? 'linear-gradient(135deg, #8B5CF6 0%, #7C3AED 25%, #6D28D9 50%, #5B21B6 75%, #1F1B24 100%)'
            : 'linear-gradient(135deg, #A855F7 0%, #9333EA 25%, #7C3AED 50%, #6D28D9 75%, #F8FAFC 100%)'
        }}
      />

      {/* Additional gradient overlay for depth */}
      <div
        className="absolute inset-0 w-full h-full opacity-60"
        style={{
          background: isDark
            ? 'radial-gradient(ellipse at top left, rgba(139, 92, 246, 0.8) 0%, rgba(124, 58, 237, 0.4) 40%, rgba(0, 0, 0, 0.8) 100%)'
            : 'radial-gradient(ellipse at top left, rgba(168, 85, 247, 0.6) 0%, rgba(147, 51, 234, 0.3) 40%, rgba(248, 250, 252, 0.9) 100%)'
        }}
      />

      {/* Header */}
      <header className="relative z-10 flex items-center justify-between px-4 md:px-[54px] py-6 md:py-[49px]">
        {/* Logo */}
        <div className="text-2xl md:text-[36px] font-normal leading-[1.21] text-white">
          CLOUDY.AI
        </div>

        {/* Navigation - Hidden on mobile */}
        <nav className="hidden lg:flex items-center gap-[90px] px-[14px] py-[12px] border border-white/30 rounded-[40px] backdrop-blur-sm bg-white/10">
          {navItems.map((item) => (
            <div
              key={item}
              className={cn(
                "px-4 py-0 rounded-[20px] cursor-pointer transition-all duration-200",
                activeNav === item
                  ? "bg-white text-[#7C3AED] border border-white"
                  : "text-white border border-transparent hover:bg-white/20"
              )}
              onClick={() => setActiveNav(item)}
            >
              <span className="text-[20px] font-normal leading-[1.21] text-center">
                {item}
              </span>
            </div>
          ))}
        </nav>

        {/* Right side - Theme Toggle and Register Button */}
        <div className="flex items-center gap-2 md:gap-4">
          <ThemeToggle />
          <Button
            onClick={handleRegister}
            className={cn(
              "bg-[#752DE0] hover:bg-[#752DE0]/90 text-white",
              "px-4 md:px-[22px] py-2 md:py-[14px] rounded-[40px]",
              "text-lg md:text-[22px] font-normal leading-[1.21]",
              "transition-all duration-200"
            )}
          >
            Register
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex flex-col items-start px-4 md:px-[73px] mt-16 md:mt-[120px]">
        {/* Hero Emoji */}
        <div className="mb-6 md:mb-8">
          <div className="w-[66px] h-[66px] flex items-center justify-center rounded-full shadow-[0px_4px_12px_0px_rgba(0,0,0,0.25)] bg-white/10 backdrop-blur-sm">
            <Image
              src="/images/hero-emoji.svg"
              alt="AI Emoji"
              width={66}
              height={66}
              className="w-full h-full"
            />
          </div>
        </div>

        {/* Main Headline */}
        <h1 className="text-4xl md:text-[64px] font-normal leading-[1.21] text-center md:text-left mb-6 max-w-full md:max-w-[614px] text-white">
          The AI Playground{' '}
          <br />
          building the future.
        </h1>

        {/* Subtitle */}
        <p className="text-sm md:text-[16px] font-normal leading-[1.21] mb-6 md:mb-8 max-w-full md:max-w-[449px] md:ml-4 text-white/90">
          Powered by the leading coding agent, Cloudy.AI helps you build complex features across production codebases.
        </p>

        {/* Get Started Button */}
        <Button
          onClick={handleGetStarted}
          className="bg-white/90 hover:bg-white text-[#7C3AED] px-6 md:px-[22px] py-3 md:py-[14px] rounded-[40px] text-lg md:text-[22px] font-semibold leading-[1.21] transition-all duration-200 md:ml-4 shadow-lg"
        >
          Get Started
        </Button>
      </main>

      {/* Right Side Decorative Elements - Matching Figma Design */}
      <div className="hidden md:block absolute top-0 right-0 w-[45%] h-full">
        {/* Main decorative rectangle */}
        <div
          className="absolute top-[20%] right-[5%] w-[400px] h-[500px] rounded-[20px] opacity-30"
          style={{
            background: 'linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.05) 100%)',
            backdropFilter: 'blur(10px)',
            border: '1px solid rgba(255, 255, 255, 0.1)'
          }}
        />

        {/* Additional accent elements */}
        <div
          className="absolute top-[15%] right-[15%] w-[200px] h-[200px] rounded-full opacity-20"
          style={{
            background: 'radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0) 70%)'
          }}
        />

        <div
          className="absolute bottom-[25%] right-[8%] w-[150px] h-[150px] rounded-[15px] opacity-25"
          style={{
            background: 'linear-gradient(45deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.05) 100%)',
            backdropFilter: 'blur(5px)'
          }}
        />
      </div>
    </div>
  );
}
