'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/Button';
import { ThemeToggle } from '@/components/features/ThemeToggle';
import { useTheme } from '@/hooks/useTheme';
import { cn } from '@/lib/utils';
import Image from 'next/image';

export function LandingPage() {
  const router = useRouter();
  const { resolvedTheme } = useTheme();
  const [activeNav, setActiveNav] = useState('Home');

  const navItems = ['Home', 'Pricing', 'Features', 'Contact'];

  const handleGetStarted = () => {
    router.push('/chat');
  };

  const handleRegister = () => {
    // You can implement registration logic here
    router.push('/chat');
  };

  // Theme-aware colors
  const isDark = resolvedTheme === 'dark';

  return (
    <div className={cn(
      "min-h-screen overflow-hidden relative transition-colors duration-300",
      isDark
        ? "bg-[#1E1E1E] text-white"
        : "bg-slate-50 text-slate-900"
    )}>
      {/* Background gradient circle */}
      <div
        className="absolute -top-[86px] -left-[480px] w-[1200px] h-[1200px] rounded-full opacity-80"
        style={{
          background: isDark
            ? 'radial-gradient(circle at 50% 50%, rgba(117, 45, 224, 1) 0%, rgba(64, 25, 122, 0) 100%)'
            : 'radial-gradient(circle at 50% 50%, rgba(117, 45, 224, 0.3) 0%, rgba(64, 25, 122, 0) 100%)'
        }}
      />

      {/* Header */}
      <header className="relative z-10 flex items-center justify-between px-4 md:px-[54px] py-6 md:py-[49px]">
        {/* Logo */}
        <div className={cn(
          "text-2xl md:text-[36px] font-normal leading-[1.21]",
          isDark ? "text-white" : "text-slate-900"
        )}>
          CLOUDY.AI
        </div>

        {/* Navigation - Hidden on mobile */}
        <nav className={cn(
          "hidden lg:flex items-center gap-[90px] px-[14px] py-[12px] border rounded-[40px]",
          isDark ? "border-white/30" : "border-slate-300"
        )}>
          {navItems.map((item) => (
            <div
              key={item}
              className={cn(
                "px-4 py-0 rounded-[20px] cursor-pointer transition-all duration-200",
                activeNav === item
                  ? isDark
                    ? "bg-white text-[#1E1E1E] border border-[#1E1E1E]"
                    : "bg-slate-900 text-white border border-slate-900"
                  : isDark
                    ? "text-white border border-[#752DE0] hover:bg-[#752DE0]/10"
                    : "text-slate-700 border border-[#752DE0] hover:bg-[#752DE0]/10"
              )}
              onClick={() => setActiveNav(item)}
            >
              <span className="text-[20px] font-normal leading-[1.21] text-center">
                {item}
              </span>
            </div>
          ))}
        </nav>

        {/* Right side - Theme Toggle and Register Button */}
        <div className="flex items-center gap-2 md:gap-4">
          <ThemeToggle />
          <Button
            onClick={handleRegister}
            className={cn(
              "bg-[#752DE0] hover:bg-[#752DE0]/90 text-white",
              "px-4 md:px-[22px] py-2 md:py-[14px] rounded-[40px]",
              "text-lg md:text-[22px] font-normal leading-[1.21]",
              "transition-all duration-200"
            )}
          >
            Register
          </Button>
        </div>
      </header>

      {/* Main Content */}
      <main className="relative z-10 flex flex-col items-start px-4 md:px-[73px] mt-16 md:mt-[240px]">
        {/* Hero Emoji */}
        <div className="mb-6 md:mb-8">
          <div className="w-[60px] h-[60px] flex items-center justify-center rounded-full shadow-[0px_1px_3px_0px_rgba(0,0,0,0.35)]">
            <Image
              src="/images/hero-emoji.svg"
              alt="AI Emoji"
              width={60}
              height={60}
              className="w-full h-full"
            />
          </div>
        </div>

        {/* Main Headline */}
        <h1 className={cn(
          "text-4xl md:text-[64px] font-normal leading-[1.21] text-center md:text-left mb-6 max-w-full md:max-w-[614px]",
          isDark ? "text-white" : "text-slate-900"
        )}>
          The AI Playground{' '}
          <br />
          building the future.
        </h1>

        {/* Subtitle */}
        <p className={cn(
          "text-sm md:text-[16px] font-normal leading-[1.21] mb-6 md:mb-8 max-w-full md:max-w-[449px] md:ml-4",
          isDark ? "text-white" : "text-slate-700"
        )}>
          Powered by the leading coding agent, Cloudy.AI helps you build complex features across production codebases.
        </p>

        {/* Get Started Button */}
        <Button
          onClick={handleGetStarted}
          className={cn(
            isDark
              ? "bg-white/78 hover:bg-white/90 text-[#752DE0]"
              : "bg-[#752DE0] hover:bg-[#752DE0]/90 text-white",
            "px-6 md:px-[22px] py-3 md:py-[14px] rounded-[40px]",
            "text-lg md:text-[22px] font-semibold leading-[1.21]",
            "transition-all duration-200 md:ml-4"
          )}
        >
          Get Started
        </Button>
      </main>

      {/* Decorative Rectangle - Hidden on mobile */}
      <div className="hidden md:block absolute bottom-0 right-[71px] w-[600px] h-[600px] bg-gradient-to-br from-[#752DE0]/20 to-transparent rounded-lg" />
    </div>
  );
}
