export interface AIModel {
  id: string;
  name: string;
  provider: string;
  description: string;
  maxTokens: number;
  costPer1kTokens: number;
  icon: string;
}

export interface ModelParameters {
  temperature: number;
  maxTokens: number;
  topP: number;
  frequencyPenalty: number;
  presencePenalty: number;
}

export interface PromptTemplate {
  id: string;
  name: string;
  description: string;
  content: string;
  category: 'creative' | 'technical' | 'analysis' | 'custom';
  parameters: ModelParameters;
  createdAt: string;
}

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: string;
  tokens?: number;
  model?: string;
}

export interface Conversation {
  id: string;
  title: string;
  messages: ChatMessage[];
  model: string;
  parameters: ModelParameters;
  createdAt: string;
  updatedAt: string;
}

export type Theme = 'light' | 'dark' | 'system';
export type ExportFormat = 'json' | 'txt' | 'markdown';